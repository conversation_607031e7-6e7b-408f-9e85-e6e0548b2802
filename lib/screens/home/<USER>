import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../screens/profile/profile_screen.dart';
import '../../screens/settings/settings_page.dart';
import '../../localization/app_localizations_extension.dart';
import '../../config/app_config.dart';
import '../../providers/user_provider.dart';
import '../../models/course_model.dart';
import '../../screens/courses/courses_page.dart';
import '../../theme/app_theme.dart';
import '../../widgets/feature_card.dart';
import '../career/career_page.dart';
import '../courses/enrolled_courses_page.dart';
import '../library/library_items_page.dart';

class Dashboard extends StatefulWidget {
  const Dashboard({super.key});

  @override
  DashboardState createState() => DashboardState();
}

class DashboardState extends State<Dashboard> {
  int _selectedIndex = 0;

  final List<Widget> _pages = [
    HomePage(),
    CoursesPage(),
    LibraryItemsPage(),
    CareerPage()
  ];

  Future<List<Course>> fetchCourses() async {
    final response = await http.get(
      Uri.parse('${AppConfig.apiBaseUrl}${AppConfig.apiGetCourseList}'),
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> jsonResponse = json.decode(response.body);
      final List<dynamic> coursesJson = jsonResponse['payload']['courses_data'];
      return coursesJson.map((json) => Course.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load courses');
    }
  }

  // Function to handle Bottom Navigation Bar item taps
  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  // Function to switch to the Courses tab
  void navigateTo(int index) {
    setState(() {
      _selectedIndex = index; // Index of the "Courses" tab
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // Use IndexedStack to preserve the state of all pages
      body: IndexedStack(
        index: _selectedIndex,
        children: _pages,
      ),

      // Bottom Navigation Bar
      bottomNavigationBar: BottomNavigationBar(
        backgroundColor: Theme.of(context).bottomAppBarTheme.color,
        selectedItemColor: Theme.of(context).bottomAppBarTheme.color,
        unselectedItemColor: Theme.of(context).bottomAppBarTheme.color,
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.home),
            label: context.tr('navigation.home'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.menu_book),
            label: context.tr('navigation.courses'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.account_balance_wallet_rounded),
            label: context.tr('navigation.library'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.work),
            label: context.tr('navigation.career'),
          ),
        ],
      ),
    );
  }
}

// Updated HomePageContent to use the callback
class HomePage extends StatefulWidget {
  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
    // Load user data from UserProvider if not already loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      if (userProvider.user == null) {
        userProvider.loadUserFromDatabase();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Access the navigateToCourses function
    final DashboardState? parentState =
        context.findAncestorStateOfType<DashboardState>();

    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        // Get user data from provider
        final user = userProvider.user;
        final String displayName = user?.firstName ?? 'User';
        final String? avatarUrl = user?.avatar;

        return PopScope(
            canPop: false,
            onPopInvoked: (didPop) async {
              if (!didPop) {
                bool shouldExit = await _showExitConfirmation(context);
                if (shouldExit) {
                  SystemNavigator.pop();
                }
              }
            },
            child: Scaffold(
              appBar: AppBar(
                backgroundColor: Theme.of(context).primaryColor,
                leading: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: GestureDetector(
                    onTap: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (_) => const ProfileScreen()));
                    },
                    child: CircleAvatar(
                      backgroundImage: avatarUrl != null && avatarUrl.isNotEmpty
                          ? NetworkImage('${AppConfig.apiBaseUrl}$avatarUrl')
                          : const AssetImage('assets/images/profile.png')
                              as ImageProvider, // Placeholder image
                    ),
                  ),
                ),
                title: Text(
                    context.tr('home.greeting', args: {'name': displayName})),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.settings),
                    onPressed: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (_) => const SettingsPage()));
                    },
                  ),
                ],
              ),
              body: SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Feature cards section
                    Container(
                      margin: const EdgeInsets.only(top: 8.0, bottom: 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          FeatureCard(
                            imageUrl: AppConfig.iconCourses,
                            title: context.tr('home.all_courses'),
                            description: context.tr('home.courses_description'),
                            onExplore: () {
                              parentState?.navigateTo(1);
                            },
                          ),
                          FeatureCard(
                            imageUrl: AppConfig.iconLibrary,
                            title: context.tr('home.digital_library'),
                            description: context.tr('home.library_description'),
                            onExplore: () {
                              parentState?.navigateTo(2);
                            },
                          ),
                          FeatureCard(
                            imageUrl: AppConfig.iconCareer,
                            title: context.tr('home.career_guidance'),
                            description: context.tr('home.career_description'),
                            onExplore: () {
                              parentState?.navigateTo(3);
                            },
                          ),
                        ],
                      ),
                    ),
                    // My Courses Section
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          context.tr('home.my_courses'),
                          style: TextStyle(fontSize: AppTheme.fontSizeLarge, fontWeight: FontWeight.bold),
                        ),
                        TextButton(
                          onPressed: () {
                            parentState?.navigateTo(1);
                          },
                          child: Text(context.tr('home.view_all')),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // Wrap CoursesPage() with a Container or SizedBox
                    Container(
                      child: EnrolledCoursesPage(), // Ensure CoursesPage fits within this size
                    ),
                  ],
                ),
              ),
            ));
      },
    );
  }

  Future<bool> _showExitConfirmation(BuildContext context) async {
    final shouldExit = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('home.exit_app')),
        content: Text(context.tr('home.exit_app_text')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false), // Don't exit
            child: Text(context.tr('common.cancel')),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true), // Confirm exit
            child: Text(context.tr('common.exit')),
          ),
        ],
      ),
    );

    return shouldExit ?? false;
  }
}
