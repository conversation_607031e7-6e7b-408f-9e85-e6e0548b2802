import 'package:ZABAI/config/app_config.dart';
import 'package:ZABAI/models/course_model.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../../services/api_service.dart';
import '../../services/course_service.dart';
import '../../services/logger_service.dart';
import '../../services/image_upload_service.dart';
import '../../widgets/progres_widget.dart';
import '../../widgets/user_experience_card.dart';
import '../settings/settings_page.dart';
import '../../localization/app_localizations_extension.dart';
import '../../providers/user_provider.dart';
import '../../models/user_model.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  bool _isEditing = false;
  bool _progressLoaded = false;
  late List<EnrolledCourse> enrolledCourse;
  late List<Course> allCourse;

  // Controllers for editable fields
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _summaryController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Refresh user data when the screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<UserProvider>(context, listen: false).refreshProfile();
    });

    _prepareProgress();
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _summaryController.dispose();
    super.dispose();
  }

  // Initialize text controllers with user data
  void _initializeControllers(User user) {
    _firstNameController.text = user.firstName;
    _lastNameController.text = user.lastName;
    _summaryController.text = user.summary;
  }

  // Pick and upload avatar image immediately
  Future<void> _pickAndUploadAvatar() async {
    try {
      LoggerService.info('Starting avatar update process');

      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Updating avatar...'),
            ],
          ),
        ),
      );

      final File? imageFile = await ImageUploadService.pickImageSafely(context);

      if (imageFile != null && mounted) {
        LoggerService.info('Image selected, uploading: ${imageFile.path}');

        // Get user provider for later refresh
        final userProvider = Provider.of<UserProvider>(context, listen: false);

        // Upload only the avatar file
        final response = await ImageUploadService.uploadAvatar(imageFile);

        // Close loading dialog
        if (mounted) Navigator.of(context).pop();

        if (mounted) {
          // Check if response is successful
          // New API returns profile data directly (no status field) when successful
          // Error responses will have 'error' and 'status' fields
          if (response.containsKey('error')) {
            // This is an error response
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to update avatar: ${response['error']}'),
                backgroundColor: Colors.red,
              ),
            );
          } else if (response.containsKey('id')) {
            // This is a successful response with profile data
            // Refresh user data to get updated avatar
            await userProvider.refreshProfile();

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Avatar updated successfully!'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } else {
            // Fallback: assume success if no error field
            await userProvider.refreshProfile();

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Avatar updated successfully!'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          }
        }
      } else {
        // Close loading dialog if no image selected
        if (mounted) Navigator.of(context).pop();

        LoggerService.warning('No image was selected');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No image selected')),
          );
        }
      }
    } catch (e) {
      // Close loading dialog on error
      if (mounted) Navigator.of(context).pop();

      LoggerService.error('Error updating avatar: $e', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update avatar: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Save profile changes
  Future<void> _saveProfile() async {
    if (!mounted) return;

    try {
      // Prepare data to update
      final updatedData = {
        'first_name': _firstNameController.text,
        'last_name': _lastNameController.text,
        'summary': _summaryController.text,
      };

      // Get the user provider
      final userProvider = Provider.of<UserProvider>(context, listen: false);

      // Update profile using the provider (text data only)
      final bool success = await userProvider.updateProfile(updatedData);

      if (!mounted) return;

      if (success) {
        // Exit editing mode
        setState(() {
          _isEditing = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.tr('profile.profile_updated'))),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.tr('profile.update_failed'))),
        );
      }
    } catch (e) {
      LoggerService.error('Profile update error', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.tr('profile.update_failed'))),
        );
      }
    }
  }

  Future<void> _prepareProgress() async {
    print("****\nHERE LOADING PROGRESS DATA !!\n***");

  enrolledCourse = await CourseService.fetchEnrolledCourses();
  allCourse = await CourseService.fetchCourses();
  setState(() {
    _progressLoaded = true;
  });
}

  @override
  Widget build(BuildContext context) {
    return Consumer<UserProvider>(builder: (context, userProvider, child) {
      // Initialize controllers when user data is available
      if (userProvider.user != null && !_isEditing) {
        _initializeControllers(userProvider.user!);
      }

      return Scaffold(
        appBar: AppBar(
          title: Text(context.tr('profile.my_profile')),
          actions: [
            // Cancel button (only in edit mode)
            if (_isEditing)
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  // Cancel editing and reset controllers
                  setState(() {
                    _isEditing = false;
                    if (userProvider.user != null) {
                      _initializeControllers(userProvider.user!);
                    }
                  });
                },
              ),

            // Edit/Save button
            if (!userProvider.isLoading && userProvider.user != null)
              IconButton(
                icon: Icon(_isEditing ? Icons.save : Icons.edit),
                onPressed: () {
                  if (_isEditing) {
                    // Save changes
                    _saveProfile();
                  } else {
                    // Enter edit mode
                    setState(() {
                      _isEditing = true;
                    });
                  }
                },
              ),
            // Settings button (only when not editing)
            if (!_isEditing)
              IconButton(
                icon: const Icon(Icons.settings),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const SettingsPage()),
                  );
                },
              ),
          ],
        ),
        body: _buildBody(userProvider),
      );
    });
  }

  Widget _buildBody(UserProvider userProvider) {
    if (userProvider.isLoading && userProvider.user == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (userProvider.error.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              userProvider.error,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => userProvider.refreshProfile(),
              child: Text(context.tr('errors.try_again')),
            ),
          ],
        ),
      );
    }

    if (userProvider.user == null) {
      return Center(
        child: Text(context.tr('profile.no_profile_data')),
      );
    }

    return _buildProfileContent(userProvider.user!);
  }

  Widget _buildProfileContent(User user) {
    return LayoutBuilder(builder: (context, constraints){
      final double screenWidth = constraints.maxWidth;
      final bool isTablet = screenWidth > 600;

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile header with cover photo and avatar
            _buildProfileHeader(user),

            // Profile details
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 24.0 : 16.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile information
                  _buildProfileInfo(user),
                ],
              ),
            ),

            //user progress
            _progressLoaded
              ?UserProgressWidget(
                enrolledCourses:enrolledCourse,
                allCourses: allCourse,
              ): const Center(child: CircularProgressIndicator()),

            const SizedBox(height: 48)
          ],
        ),
      );
    });
  }

  // Build profile header with cover photo and avatar
  Widget _buildProfileHeader(User user) {
    // Get placeholder URLs if profile data doesn't have images
    final String coverPhotoUrl = user.coverPhoto.isNotEmpty
        ? user.coverPhoto
        : ApiService.getPlaceholderImageUrl('cover');
    final String avatarUrl = user.avatar.isNotEmpty
        ? user.avatar
        : ApiService.getPlaceholderImageUrl('avatar');

    return LayoutBuilder(builder: (context, constraints) {
      // Calculate responsive dimensions
      final double screenWidth = constraints.maxWidth;
      final bool isTablet = screenWidth > 600;

      // Responsive dimensions
      final double coverHeight = isTablet ? 80.0 : 60.0;
      final double avatarSize = isTablet ? 120.0 : 100.0;
      final double headerHeight = coverHeight + (avatarSize / 2) + 10;
      final double borderWidth = isTablet ? 5.0 : 4.0;

      LoggerService.debug(
          'Profile images - Cover: $coverPhotoUrl, Avatar: $avatarUrl, Width: $screenWidth');

      return SizedBox(
        height: headerHeight,
        width: double.infinity,
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.bottomCenter,
          children: [
            // Cover photo - positioned at the top
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: coverHeight,
              child: Container(
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            Positioned(
              bottom: 0,
              child: GestureDetector(
                onTap: _pickAndUploadAvatar,
                child: Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border:
                            Border.all(color: Colors.white, width: borderWidth),
                      ),
                      child: ClipOval(
                        child: SizedBox(
                          width: avatarSize,
                          height: avatarSize,
                          child: avatarUrl == 'USE_LOCAL_ASSET'
                              // Use local asset for placeholder
                              ? Image.asset(
                                  'assets/images/profile.png',
                                  fit: BoxFit.cover,
                                )
                              // Use network image for actual profile photo
                              : Image.network(
                                  "${AppConfig.apiBaseUrl}$avatarUrl",
                                  fit: BoxFit.cover,
                                  loadingBuilder:
                                      (context, child, loadingProgress) {
                                    if (loadingProgress == null) {
                                      return child;
                                    }
                                    return Container(
                                      color: Colors.grey[300],
                                      child: Center(
                                        child: CircularProgressIndicator(
                                          value: loadingProgress
                                                      .expectedTotalBytes !=
                                                  null
                                              ? loadingProgress
                                                      .cumulativeBytesLoaded /
                                                  loadingProgress
                                                      .expectedTotalBytes!
                                              : null,
                                          strokeWidth: 2.0,
                                        ),
                                      ),
                                    );
                                  },
                                  errorBuilder: (context, error, stackTrace) {
                                    LoggerService.warning(
                                        'Failed to load avatar image: $error');
                                    // Use local asset as fallback
                                    return Image.asset(
                                      'assets/images/profile.png',
                                      fit: BoxFit.cover,
                                    );
                                  },
                                ),
                        ),
                      ),
                    ),
                    // Camera overlay - always visible for avatar updates
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        padding: const EdgeInsets.all(8),
                        child: Icon(
                          Icons.camera_alt,
                          color: Colors.white,
                          size: isTablet ? 20 : 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  // Build profile information section
  Widget _buildProfileInfo(User user) {
    return LayoutBuilder(builder: (context, constraints) {
      // Calculate responsive dimensions
      final double screenWidth = constraints.maxWidth;
      final bool isTablet = screenWidth > 600;

      // Responsive dimensions
      final double nameSize = isTablet ? 28.0 : 24.0;
      final double usernameSize = isTablet ? 18.0 : 16.0;
      final double cardPadding = isTablet ? 24.0 : 16.0;
      final double sectionSpacing = isTablet ? 30.0 : 20.0;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Full name
          _isEditing
              ? Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: isTablet ? 32.0 : 16.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _firstNameController,
                          decoration: InputDecoration(
                            labelText: context.tr('auth.first_name'),
                            border: const OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextField(
                          controller: _lastNameController,
                          decoration: InputDecoration(
                            labelText: context.tr('auth.last_name'),
                            border: const OutlineInputBorder(),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              : Text(
                  user.fullName,
                  style: TextStyle(
                    fontSize: nameSize,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),

          // const SizedBox(height: 10),
          //
          // // Username
          // Text(
          //   '@${user.username}',
          //   style: TextStyle(
          //     fontSize: usernameSize,
          //     color: Colors.grey[600],
          //   ),
          // ),

          SizedBox(height: sectionSpacing),

          // Bio/Summary
          Card(
            elevation: 2,
            margin: EdgeInsets.symmetric(horizontal: isTablet ? 32.0 : 0), // Ensures width match
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: EdgeInsets.all(cardPadding), // Same padding logic
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _isEditing
                      ? TextField(
                    controller: _summaryController,
                    decoration: InputDecoration(
                      labelText: context.tr('profile.bio'),
                      alignLabelWithHint: true,
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.all(12), // Optional for compactness
                    ),
                    maxLines: isTablet ? 8 : 5,
                    style: TextStyle(fontSize: isTablet ? 16 : 14),
                  )
                      : Text(
                    user.summary.isNotEmpty
                        ? user.summary
                        : context.tr('profile.no_bio'),
                    style: TextStyle(fontSize: isTablet ? 16 : 14),
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: sectionSpacing),

          // Email and phone in a card
          Card(
            elevation: 2,
            margin: EdgeInsets.symmetric(horizontal: isTablet ? 32.0 : 0),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: EdgeInsets.all(isTablet ? 14 : 10), // reduced padding
              child: Column(
                children: [
                  // Email
                  ListTile(
                    leading: Icon(Icons.email, size: isTablet ? 24 : 20),
                    title: Text(
                      context.tr('auth.email'),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: isTablet ? 16 : 14,
                      ),
                    ),
                    subtitle: Text(
                      user.email.isNotEmpty
                          ? user.email
                          : context.tr('profile.no_bio'),
                      style: TextStyle(fontSize: isTablet ? 14 : 13),
                    ),
                    dense: true,
                    isThreeLine: false,
                    visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
                    contentPadding: EdgeInsets.zero,
                  ),

                  const Divider(height: 1),

                  // Phone
                  ListTile(
                    leading: Icon(Icons.phone, size: isTablet ? 24 : 20),
                    title: Text(
                      context.tr('auth.phone'),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: isTablet ? 16 : 14,
                      ),
                    ),
                    subtitle: Text(
                      user.phone.isNotEmpty
                          ? user.phone
                          : context.tr('profile.no_phone'),
                      style: TextStyle(fontSize: isTablet ? 14 : 13),
                    ),
                    dense: true,
                    isThreeLine: false,
                    visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: sectionSpacing),

          // experiences
          Column(
            children: [
              UserExperienceCard(
                experiences: user.experiences,
                isTablet: isTablet,
                cardPadding: 16,
              ),
            ],
          ),

          // Add bottom padding for scrolling
          SizedBox(height: isTablet ? 40 : 24),
        ],
      );
    });
  }
}


