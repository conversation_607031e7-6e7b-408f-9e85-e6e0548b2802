class Course {
  final int id;
  final String banner;
  final String title;
  final String slug;
  final String description;
  final String language;
  final String category;
  final List<String> tags;
  final List<Module> modules;

  Course({
    required this.id,
    required this.banner,
    required this.title,
    required this.slug,
    required this.description,
    this.language = '',
    this.category = '',
    this.tags = const [],
    required this.modules,
  });

  factory Course.fromJson(Map<String, dynamic> json) {
    return Course(
      id: json['id'] ?? 0,
      banner: json['course_banner'] ?? '',
      title: json['course_title'] ?? 'Untitled Course',
      slug: json['course_slug'] ?? '',
      description: json['description'] ?? 'No description available.',
      language: json['language'] ?? '',
      category: json['category'] ?? '',
      tags: (json['tags'] as List?)?.map((tag) => tag.toString()).toList() ?? [],
      modules: (json['modules'] as List?)
          ?.map((module) => Module.fromJson(module))
          .toList() ??
          [],
    );
  }
}

/// Enrolled course model for courses the user is enrolled in
/// This represents the response from apiMyCourses endpoint
class EnrolledCourse {
  final int id;
  final double completionStatus;
  final Map<String, dynamic>? certificate;
  final List<EnrolledModule> modules;

  EnrolledCourse({
    required this.id,
    required this.completionStatus,
    this.certificate,
    required this.modules,
  });

  factory EnrolledCourse.fromJson(Map<String, dynamic> json) {
    return EnrolledCourse(
      id: json['id'] ?? 0,
      completionStatus: (json['completion_status'] ?? 0.0).toDouble(),
      certificate: json['certificate'] as Map<String, dynamic>?,
      modules: (json['modules'] as List?)
          ?.map((module) => EnrolledModule.fromJson(module))
          .toList() ??
          [],
    );
  }
}

/// Enrolled module model for modules within enrolled courses
class EnrolledModule {
  final int id;
  final bool completionStatus;
  final bool lock;
  final Map<String, dynamic>? state;

  EnrolledModule({
    required this.id,
    required this.completionStatus,
    required this.lock,
    this.state,
  });

  factory EnrolledModule.fromJson(Map<String, dynamic> json) {
    return EnrolledModule(
      id: json['id'] ?? 0,
      completionStatus: json['completion_status'] ?? false,
      lock: !(json['accessible'] ?? false),
      state: json['state'] as Map<String, dynamic>?,
    );
  }
}

class Module {
  final int id;
  final String courseName;
  final String description;
  final String name;
  final String moduleSlug;
  final String downloadLink;
  final String scormDataPath;

  Module({
    required this.id,
    this.courseName = '',
    this.description = '',
    required this.name,
    required this.moduleSlug,
    required this.downloadLink,
    required this.scormDataPath,
  });

  factory Module.fromJson(Map<String, dynamic> json) {
    return Module(
      id: json['id'] ?? 0,
      courseName: json['course_name'] ?? '',
      description: json['description'] ?? '',
      name: json['name'] ?? 'Untitled Module',
      moduleSlug: json['module_slug'] ?? '',
      downloadLink: json['download_link'] ?? '',
      scormDataPath: json['scorm_data_path'] ?? '',
    );
  }
}
