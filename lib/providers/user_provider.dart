import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../repositories/user_repository.dart';
import '../services/api_service.dart';
import '../services/logger_service.dart';
import '../services/secure_storage_service.dart';
import '../services/image_upload_service.dart';
import 'dart:io';

/// Provider to manage user data and state
class UserProvider extends ChangeNotifier {
  User? _user;
  bool _isLoading = false;
  String _error = '';
  Map<String, dynamic>? _fieldErrors;
  final UserRepository _repository = UserRepository();

  // Callback for post-login actions
  Function()? _onLoginSuccess;

  // Callback for clearing user data (when user logs out or new user logs in)
  Function()? _onUserChange;

  // Getters
  User? get user => _user;
  bool get isLoading => _isLoading;
  String get error => _error;
  Map<String, dynamic>? get fieldErrors => _fieldErrors;
  bool get isLoggedIn => _user != null;

  /// Initialize the provider
  Future<void> init() async {
    await loadUserFromDatabase();
  }

  /// Set callback for post-login actions
  void setOnLoginSuccessCallback(Function()? callback) {
    _onLoginSuccess = callback;
  }

  /// Set callback for clearing user data when user changes
  void setOnUserChangeCallback(Function()? callback) {
    _onUserChange = callback;
  }

  /// Load user from database
  Future<void> loadUserFromDatabase() async {
    try {
      _isLoading = true;
      notifyListeners();

      final user = await _repository.getUser();
      _user = user;

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load user data: ${e.toString()}';
      LoggerService.error(_error, e);
      notifyListeners();
    }
  }

  /// Login user
  Future<bool> login(String username, String password) async {
    try {
      _isLoading = true;
      _error = '';
      _fieldErrors = null;
      notifyListeners();

      // Call API service to login
      final response = await ApiService.login(username, password);

      if (response['status'] == 200 && response['payload'] != null) {
        // Clear previous user data before setting new user
        if (_onUserChange != null) {
          _onUserChange!();
        }

        // Create user from login response
        final user = User.fromLoginResponse(response);

        // Save token to secure storage
        await SecureStorageService.saveToken(user.token);

        // Save user to database
        await _repository.saveUser(user);

        // Set current user
        _user = user;

        // Fetch profile data to get additional user information
        await refreshProfile();

        // Call post-login callback if set
        if (_onLoginSuccess != null) {
          _onLoginSuccess!();
        }

        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _isLoading = false;
        _error = response['error'] ?? 'Login failed';

        // Store field-specific errors if available
        if (response.containsKey('field_errors') &&
            response['field_errors'] is Map) {
          _fieldErrors = Map<String, dynamic>.from(response['field_errors']);
          LoggerService.debug('Field errors stored in provider: $_fieldErrors');
        }

        notifyListeners();
        return false;
      }
    } catch (e) {
      _isLoading = false;
      _error = 'Login failed: ${e.toString()}';
      _fieldErrors = null;
      LoggerService.error(_error, e);
      notifyListeners();
      return false;
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      _isLoading = true;
      notifyListeners();

      // Clear user data from other providers
      if (_onUserChange != null) {
        _onUserChange!();
      }

      // Clear token from secure storage
      await SecureStorageService.deleteToken();

      // Delete user from database
      await _repository.deleteAllUsers();

      // Clear current user
      _user = null;

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Logout failed: ${e.toString()}';
      LoggerService.error(_error, e);
      notifyListeners();
    }
  }

  /// Update user profile
  Future<bool> updateProfile(Map<String, dynamic> profileData) async {
    try {
      if (_user == null) {
        _error = 'No user logged in';
        notifyListeners();
        return false;
      }

      _isLoading = true;
      notifyListeners();

      // Call API to update profile
      final response = await ApiService.updateProfile(profileData);

      // Check if response is successful
      // New API returns profile data directly (no status field) when successful
      // Error responses will have 'error' and 'status' fields
      if (response.containsKey('error')) {
        // This is an error response
        _isLoading = false;
        _error = response['error'] ?? 'Failed to update profile';
        notifyListeners();
        return false;
      } else if (response.containsKey('id')) {
        // This is a successful response with profile data
        // Update user with the returned profile data
        final updatedUser = User.fromProfileResponse(response, _user!);

        // Update user in memory
        _user = updatedUser;

        // Update user in database
        await _repository.saveUser(_user!);

        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        // Fallback: assume success if no error field and update with sent data
        _user = _user!.copyWith(
          firstName: profileData['first_name'] ?? _user!.firstName,
          lastName: profileData['last_name'] ?? _user!.lastName,
          email: profileData['email'] ?? _user!.email,
          phone: profileData['phone'] ?? _user!.phone,
          summary: profileData['summary'] ?? _user!.summary,
          updatedAt: DateTime.now().toIso8601String(),
        );

        // Update user in database
        await _repository.updateUserFields(_user!.id, profileData);

        _isLoading = false;
        notifyListeners();
        return true;
      }
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to update profile: ${e.toString()}';
      LoggerService.error(_error, e);
      notifyListeners();
      return false;
    }
  }

  /// Update user profile with avatar
  Future<bool> updateProfileWithAvatar({
    required Map<String, dynamic> profileData,
    File? avatarFile,
  }) async {
    try {
      if (_user == null) {
        _error = 'No user logged in';
        notifyListeners();
        return false;
      }

      _isLoading = true;
      notifyListeners();

      // Call API to update profile with avatar
      final response = await ImageUploadService.updateProfileWithAvatar(
        profileData: profileData,
        avatarFile: avatarFile,
      );

      // Check if response is successful
      // New API returns profile data directly (no status field) when successful
      // Error responses will have 'error' and 'status' fields
      if (response.containsKey('error')) {
        // This is an error response
        _isLoading = false;
        _error = response['error'] ?? 'Failed to update profile';
        notifyListeners();
        return false;
      } else if (response.containsKey('id')) {
        // This is a successful response with profile data
        // Update user with the returned profile data
        final updatedUser = User.fromProfileResponse(response, _user!);

        // Update user in memory
        _user = updatedUser;

        // Update user in database
        await _repository.saveUser(_user!);

        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        // Fallback: refresh profile data from server to get updated avatar URL
        final refreshSuccess = await refreshProfile();

        if (!refreshSuccess) {
          // If refresh fails, update local data without avatar
          _user = _user!.copyWith(
            firstName: profileData['first_name'] ?? _user!.firstName,
            lastName: profileData['last_name'] ?? _user!.lastName,
            email: profileData['email'] ?? _user!.email,
            phone: profileData['phone'] ?? _user!.phone,
            summary: profileData['summary'] ?? _user!.summary,
            updatedAt: DateTime.now().toIso8601String(),
          );

          // Update user in database
          await _repository.updateUserFields(_user!.id, profileData);
        }

        _isLoading = false;
        notifyListeners();
        return true;
      }
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to update profile: ${e.toString()}';
      LoggerService.error(_error, e);
      notifyListeners();
      return false;
    }
  }

  /// Refresh user profile data from API
  Future<bool> refreshProfile() async {
    try {
      if (_user == null) {
        // Try to load user from database first
        await loadUserFromDatabase();

        // If still no user, we can't refresh profile
        if (_user == null) {
          _error = 'No user logged in';
          notifyListeners();
          return false;
        }
      }

      _isLoading = true;
      notifyListeners();

      // Call API to get profile data
      final response = await ApiService.getProfileData();

      if (response['status'] == 200 || response.containsKey('id')) {
        // Update user with profile data
        if (_user != null) {
          // The response is now a direct profile object, not wrapped in payload
          // Check if response has status field (old format) or direct data (new format)
          Map<String, dynamic> profileData;
          if (response.containsKey('status') && response['status'] == 200) {
            // Old format with payload wrapper
            profileData = response['payload'] ?? response;
          } else {
            // New format - direct profile data
            profileData = response;
          }

          // Create updated user by merging profile data with existing user
          final updatedUser = User.fromProfileResponse(profileData, _user!);

          // Update user in memory
          _user = updatedUser;

          // Update user in database
          await _repository.saveUser(_user!);

          _isLoading = false;
          notifyListeners();
          return true;
        } else {
          _isLoading = false;
          _error = 'User data is missing';
          notifyListeners();
          return false;
        }
      } else {
        _isLoading = false;
        _error = response['error'] ?? 'Failed to refresh profile';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to refresh profile: ${e.toString()}';
      LoggerService.error(_error, e);
      notifyListeners();
      return false;
    }
  }

  /// Clear error message
  void clearError() {
    _error = '';
    notifyListeners();
  }

  /// Clear field-specific error messages
  void clearFieldErrors() {
    _fieldErrors = null;
    notifyListeners();
  }
}
