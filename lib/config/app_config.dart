/// Configuration class for application-wide settings
class AppConfig {
  /// List of supported locales in the application
  /// Format: [languageCode, countryCode (optional)]
  static const List<Map<String, String>> supportedLocales = [
    {'languageCode': 'en', 'name': 'English'},
    {'languageCode': 'bn', 'name': 'Bengali'},
    {'languageCode': 'my', 'name': 'Burmese'},
  ];

  /// Default locale to use if the user's locale is not supported
  static const String defaultLocale = 'en';

  /// Whether to use the device locale as the default (if supported)
  static const bool useDeviceLocale = true;

  /// Enabled locales for this build
  /// To disable a locale, comment it out or remove it from this list
  static const List<String> enabledLocales = [
    'en',
    'my',
    'bn',
  ];

  /// Check if a locale is enabled in the current build
  static bool isLocaleEnabled(String localeCode) {
    return enabledLocales.contains(localeCode);
  }

  /// Get the list of enabled locales with their display names
  static List<Map<String, String>> getEnabledLocalesWithNames() {
    return supportedLocales
        .where((locale) => enabledLocales.contains(locale['languageCode']))
        .toList();
  }

  /// Get locale display name by language code
  static String getLocaleDisplayName(String languageCode) {
    final locale = supportedLocales.firstWhere(
      (locale) => locale['languageCode'] == languageCode,
      orElse: () => {'languageCode': languageCode, 'name': languageCode},
    );
    return locale['name'] ?? languageCode;
  }

  /// API Configuration

  /// Base URL for all API endpoints
  static const String apiBaseUrl = "https://zabai.org";
  // static const String apiBaseUrl = "https://edd-hub.com";
  // static const String apiBaseUrl = "http://10.0.2.2:8000";
  // static const String apiBaseUrl = "http://192.168.10.31:8000";

  /// Current language code for API endpoints
  static String _currentLanguageCode = defaultLocale;

  /// Set the current language code for API endpoints
  static void setLanguageCode(String languageCode) {
    _currentLanguageCode = languageCode;
  }

  /// Get the current language code
  static String get currentLanguageCode => _currentLanguageCode;

  /// API Endpoints (with dynamic language code)
  static String get apiRegister => "/$_currentLanguageCode/api/register";
  static String get apiLogin => "/$_currentLanguageCode/api/login";
  static String get apiVerify => "/$_currentLanguageCode/api/verify";
  static const String apiGetProfile = "/v1/api/profile";
  static String get apiUpdateProfile => "/$_currentLanguageCode/v1/api/profile";
  static const String apiGetCourseList = "/api/get-course-list";
  static const String apiLibrary = "/api/digital-library";
  static const String apiCourses = "/api/get-course-list";
  static String get apiCourseEnroll =>
      "/$_currentLanguageCode/v1/api/course/enroll";
  static String get apiMyCourses =>
      "/$_currentLanguageCode/v1/api/course/my-courses";
  static String get apiScormPlayer => "/$_currentLanguageCode/api/app-view";
  static const String apiCourseLibraryContent = "/api/course-library-content";
  static String get apiCareerGuidanceQuestions =>
      "/$_currentLanguageCode/v1/api/career-guidance/questions";
  static String get apiCareerGuidanceResponse =>
      "/$_currentLanguageCode/v1/api/career-guidance/response";

  static String get certificateDownloadLink =>
      "/$_currentLanguageCode/courses/my-certificate";

  /// External Image URLs
  static const String placeholderCoverImage = "https://www.icolorpalette.com/download/solidcolorimage/8c3573_solid_color_background_icolorpalette.png";
      // "https://picsum.photos/id/1018/400/100";
  static const String placeholderProfileImage =
      "https://picsum.photos/id/237/50/50";

  /// Feature Card Icons
  static const String iconCourses =
      "https://img.icons8.com/?size=100&id=13171&format=png&color=000000";
  static const String iconCareer =
      "https://img.icons8.com/?size=100&id=GaZyzpIg5O9B&format=png&color=000000";
  static const String iconLibrary =
      "https://img.icons8.com/?size=100&id=11873&format=png&color=000000";

  /// Library Configuration
  ///
  /// If true: Library items are downloaded and opened with native apps
  /// If false: Library items are opened directly in WebView without downloading
  static const bool libraryDownloadable = false;
}
