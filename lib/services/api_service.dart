import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';
import 'logger_service.dart';
import 'secure_storage_service.dart';

// Custom exception for API errors
class ApiException implements Exception {
  final String message;
  final int? statusCode;

  ApiException(this.message, [this.statusCode]);

  @override
  String toString() =>
      'ApiException: $message${statusCode != null ? ' (Status code: $statusCode)' : ''}';
}

class ApiService {
  static String get baseUrl => AppConfig.apiBaseUrl;

  static const Map<String, String> _headers = {
    "Content-Type": "application/json",
  };

  // Helper method to handle API responses
  static Map<String, dynamic> _handleResponse(http.Response response) {
    final statusCode = response.statusCode;
    final responseBody = response.body;

    LoggerService.debug(
        'API Response [${response.request?.url}]: Status $statusCode');

    if (statusCode >= 200 && statusCode < 300) {
      try {
        return jsonDecode(responseBody);
      } catch (e) {
        LoggerService.error('Failed to decode response', e);
        throw ApiException('Invalid response format');
      }
    } else {
      LoggerService.warning(
          'API error: Status $statusCode, Body: $responseBody');

      // Try to extract error message from response
      String errorMessage = 'Request failed with status: $statusCode';
      Map<String, dynamic> fieldErrors = {};

      try {
        final Map<String, dynamic> errorData = jsonDecode(responseBody);

        // Check for structured error format with errors in payload
        if (errorData.containsKey('payload') &&
            errorData['payload'] is Map &&
            errorData['payload'].containsKey('errors')) {
          final errors = errorData['payload']['errors'];

          // Process field-specific errors if errors is a Map
          if (errors is Map) {
            fieldErrors = Map<String, dynamic>.from(errors);

            // Extract a general error message if available
            if (errors.containsKey('__all__') &&
                errors['__all__'] is List &&
                errors['__all__'].isNotEmpty) {
              errorMessage = errors['__all__'][0];
            } else if (errors.isNotEmpty) {
              // Use the first field error as the general error message
              final firstField = errors.keys.first;
              final firstError = errors[firstField];
              if (firstError is List && firstError.isNotEmpty) {
                errorMessage = firstError[0];
              }
            }
          }
          // Handle case where errors is a List (common in login responses)
          else if (errors is List && errors.isNotEmpty) {
            errorMessage = errors[0];
            // Store the error message in a special field error key for consistency
            fieldErrors = {'message': errors};
          }
        } else {
          // Fallback to simple error message
          errorMessage =
              errorData['error'] ?? errorData['message'] ?? errorMessage;
        }
      } catch (_) {
        // If we can't parse the error, use the default message
      }

      return {
        "error": errorMessage,
        "status": statusCode,
        "field_errors": fieldErrors
      };
    }
  }

  // Register API (with confirm_password)
  static Future<Map<String, dynamic>> register(String firstname,
      String lastname, String username, String password, String confirmPassword,
      [String? email, String? phone]) async {
    try {
      LoggerService.info('Registering user: $username');

      final response = await http.post(
        Uri.parse('$baseUrl${AppConfig.apiRegister}'),
        headers: _headers,
        body: jsonEncode({
          "first_name": firstname,
          "last_name": lastname,
          "username": username,
          "password": password,
          "confirm_password": confirmPassword,
          "email": email,
          "phone": phone
        }),
      );

      final responseData = _handleResponse(response);

      // Log field-specific errors if available
      if (responseData.containsKey('field_errors') &&
          responseData['field_errors'] is Map &&
          (responseData['field_errors'] as Map).isNotEmpty) {
        LoggerService.warning(
            'Registration field errors: ${responseData['field_errors']}');
      }

      return responseData;
    } catch (e) {
      LoggerService.error('Registration error', e);
      return {"error": "Registration failed: ${e.toString()}"};
    }
  }

  // Login API
  static Future<Map<String, dynamic>> login(
      String username, String password) async {
    try {
      LoggerService.info('Logging in user: $username');

      final response = await http.post(
        Uri.parse('$baseUrl${AppConfig.apiLogin}'),
        headers: _headers,
        body: jsonEncode({
          "username": username,
          "password": password,
        }),
      );

      final responseData = _handleResponse(response);

      // Log field-specific errors if available
      if (responseData.containsKey('field_errors') &&
          responseData['field_errors'] is Map &&
          (responseData['field_errors'] as Map).isNotEmpty) {
        LoggerService.warning(
            'Login field errors: ${responseData['field_errors']}');
      }

      // Add user-friendly error messages for common status codes
      // but preserve the original error data
      if (responseData['status'] == 400 || responseData['status'] == 401) {
        if (!responseData.containsKey('error') ||
            responseData['error'] == null) {
          responseData['error'] = "Incorrect username or password";
        }
      } else if (responseData['status'] == 404) {
        if (!responseData.containsKey('error') ||
            responseData['error'] == null) {
          responseData['error'] = "Username not found";
        }
      } else if (responseData['status'] >= 500) {
        if (!responseData.containsKey('error') ||
            responseData['error'] == null) {
          responseData['error'] = "Server error. Please try again later.";
        }
      }

      return responseData;
    } catch (e) {
      LoggerService.error('Login error', e);

      // Provide more user-friendly error messages based on exception type
      Map<String, dynamic> errorResponse = {
        "error": "Unable to sign in. Please try again later."
      };

      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection refused')) {
        errorResponse["error"] =
            "Unable to connect to the server. Please check your internet connection.";
      } else if (e.toString().contains('timeout')) {
        errorResponse["error"] = "Connection timed out. Please try again.";
      }

      return errorResponse;
    }
  }

  // Verify OTP
  static Future<Map<String, dynamic>> verifyOtp(String otp) async {
    try {
      LoggerService.info('Verifying OTP');

      final response = await http.post(
        Uri.parse('$baseUrl${AppConfig.apiVerify}'),
        headers: _headers,
        body: jsonEncode({
          'otp': otp,
        }),
      );

      final responseData = _handleResponse(response);

      if (responseData['error'] != null) {
        return {
          'status': 'failure',
          'message': responseData['error'],
        };
      }

      if (responseData['status'] == 200) {
        return {
          'status': 'success',
          'message':
              responseData['payload']?['message'] ?? 'Verification successful',
        };
      } else {
        return {
          'status': 'failure',
          'message': 'Unexpected response structure',
        };
      }
    } catch (e) {
      LoggerService.error('OTP verification error', e);
      return {
        'status': 'failure',
        'message': 'Verification failed: ${e.toString()}',
      };
    }
  }

  // Fetch profile data
  static Future<Map<String, dynamic>> getProfileData() async {
    try {
      LoggerService.info('Fetching profile data');

      final token = await SecureStorageService.getToken();
      if (token == null) {
        throw ApiException('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('$baseUrl${AppConfig.apiGetProfile}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final responseData = _handleResponse(response);

      // Save profile data to SharedPreferences
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString('profile_data', jsonEncode(responseData));

      return responseData;
    } catch (e) {
      LoggerService.error('Failed to load profile data', e);
      throw ApiException('Failed to load profile data: ${e.toString()}');
    }
  }

  // Retrieve profile data from SharedPreferences
  static Future<Map<String, dynamic>?> getProfileDataFromPrefs() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? profileData = prefs.getString('profile_data');

      if (profileData != null) {
        return jsonDecode(profileData);
      }
      return null;
    } catch (e) {
      LoggerService.error('Failed to get profile data from preferences', e);
      return null;
    }
  }

  // Update profile data
  static Future<Map<String, dynamic>> updateProfile(
      Map<String, dynamic> profileData) async {
    try {
      LoggerService.info('Updating profile data');

      final token = await SecureStorageService.getToken();
      if (token == null) {
        throw ApiException('Authentication token not found');
      }

      final response = await http.put(
        Uri.parse('$baseUrl${AppConfig.apiUpdateProfile}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(profileData),
      );

      final responseData = _handleResponse(response);

      // Update cached profile data
      // If response contains profile data (has 'id' field), update cache
      if (responseData.containsKey('id') &&
          !responseData.containsKey('error')) {
        // Save the updated profile data directly
        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString('profile_data', jsonEncode(responseData));
      }

      return responseData;
    } catch (e) {
      LoggerService.error('Failed to update profile', e);
      throw ApiException('Failed to update profile: ${e.toString()}');
    }
  }

  // Get placeholder image URL
  static String getPlaceholderImageUrl(String type) {
    // For avatar, we'll use a local asset (handled differently in the UI)
    // For cover, we'll still use picsum.photos
    switch (type) {
      case 'avatar':
        // This is just a marker - we'll handle this specially in the UI
        return 'USE_LOCAL_ASSET';
      case 'cover':
        // Use a landscape image for cover
        return AppConfig.placeholderCoverImage;
      default:
        return AppConfig.placeholderProfileImage; // Default image
    }
  }

  // Logout - clear token
  static Future<void> logout() async {
    try {
      LoggerService.info('Logging out user');
      await SecureStorageService.deleteToken();

      // Clear any other user data
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.remove('profile_data');
    } catch (e) {
      LoggerService.error('Logout error', e);
      throw ApiException('Failed to logout: ${e.toString()}');
    }
  }
}
